import React from "react";

export const IconComponentNode = ({ className }) => {
  return (
    <svg
      className={`icon-component-node ${className}`}
      fill="none"
      height="151"
      viewBox="0 0 92 151"
      width="92"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        className="mask"
        fill="black"
        height="187.124"
        id="path-1-outside-1_4477_6703"
        maskUnits="userSpaceOnUse"
        width="166.021"
        x="-41.3763"
        y="-24.841"
      >
        <rect
          className="rect"
          fill="white"
          height="187.124"
          width="166.021"
          x="-41.3763"
          y="-24.841"
        />

        <path
          className="path"
          d="M6.15488 6.02725C21.9543 6.05367 37.391 10.7646 50.513 19.5642C63.6351 28.3638 73.853 40.857 79.8748 55.4638C85.8965 70.0707 87.4516 86.1351 84.3434 101.626C81.2352 117.116 73.6033 131.338 62.4127 142.491L6.02131 85.9104L6.15488 6.02725Z"
        />
      </mask>

      <path
        className="path"
        d="M6.15488 6.02725C21.9543 6.05367 37.391 10.7646 50.513 19.5642C63.6351 28.3638 73.853 40.857 79.8748 55.4638C85.8965 70.0707 87.4516 86.1351 84.3434 101.626C81.2352 117.116 73.6033 131.338 62.4127 142.491L6.02131 85.9104L6.15488 6.02725Z"
        fill="#FFD4C4"
      />

      <path
        className="path"
        d="M6.15488 6.02725C21.9543 6.05367 37.391 10.7646 50.513 19.5642C63.6351 28.3638 73.853 40.857 79.8748 55.4638C85.8965 70.0707 87.4516 86.1351 84.3434 101.626C81.2352 117.116 73.6033 131.338 62.4127 142.491L6.02131 85.9104L6.15488 6.02725Z"
        mask="url(#path-1-outside-1_4477_6703)"
        stroke="white"
        strokeWidth="12"
      />
    </svg>
  );
};
